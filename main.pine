// Facebook : Syntrade
//@version=6
strategy(
  title = "首次测试策略", overlay=false, max_labels_count=500,
  // 实时性
  calc_on_order_fills = true,
  // 每次交易满仓
  default_qty_type = strategy.percent_of_equity,
  default_qty_value = 90,
  currency = currency.USDT,
  slippage = 6,
  // 手续费
  commission_type = strategy.commission.percent,
  commission_value = 0.075,
  use_bar_magnifier = true,
  fill_orders_on_standard_ohlc = true)

strategy.risk.allow_entry_in(strategy.direction.long)
// 参数设置
kPeriod = input.int(14, "K周期")
dPeriod = input.int(3, "D周期")
smoothK = input.int(3, "K平滑")
baseOverB = input.int(80, "基础超买线")
baseOverS = input.int(20, "基础超卖线")
// 动态阈值参数
volatilityPeriod = input.int(20, "波动率周期", minval=5, maxval=50, tooltip="计算K线波动率的周期")
signal_length = input.int(9, "信号平滑周期", minval=3, maxval=20, tooltip="动态阈值平滑周期")
sensitivity = input.float(0.5, "敏感度", minval=0.1, maxval=2.0, step=0.1, tooltip="对K线波动的敏感度")
// 提前信号参数
earlySignalThreshold = input.float(5.0, "提前信号阈值", minval=1.0, maxval=20.0, step=0.5, tooltip="K线变化幅度超过此值时触发提前信号")
earlySignalDistance = input.float(5.0, "提前信号距离", minval=1.0, maxval=15.0, step=0.5, tooltip="距离穿越线多远时开始检测提前信号")
// 计算随机指标
k = ta.sma(ta.stoch(close, high, low, kPeriod), smoothK)
d = ta.sma(k, dPeriod)

// 动态阈值计算（参考Kaufman的PPO主线算法）
// 计算K线动量，类似PPO的计算方式
kMomentum_fast = ta.ema(k, 12)  // 快速EMA
kMomentum_slow = ta.ema(k, 26)  // 慢速EMA
kPPO = (kMomentum_fast - kMomentum_slow) / kMomentum_slow * 100  // 类似PPO的计算

// 参考Kaufman的levelu/leveld逻辑计算动态阈值
levelu = 0.0, leveld = 0.0
levelu := (kPPO > 0) ? ta.ema(baseOverB + kPPO * sensitivity, signal_length) : nz(levelu[1])
leveld := (kPPO < 0) ? ta.ema(baseOverS + kPPO * sensitivity, signal_length) : nz(leveld[1])

// 动态阈值状态判断（类似Kaufman的state逻辑）
thresholdState = 0
if k < leveld
    thresholdState := -1
if k > levelu
    thresholdState := 1

// 最终动态阈值（具有Kaufman PPO主线的特征）
OverB = levelu
OverS = leveld

// 确保阈值在合理范围内
OverB := math.min(math.max(OverB, baseOverB), 95)
OverS := math.max(math.min(OverS, baseOverS), 5)

plot(k, title="%K", color=#2962FF)
plot(d, title="%D", color=#FF6D00)
// 绘制动态阈值线（参考Kaufman PPO主线的粗实线样式）
// 根据阈值状态确定颜色（类似Kaufman的colorout逻辑）
upperColor = thresholdState == 1 ? color.red : thresholdState == -1 ? color.green : color.gray
lowerColor = thresholdState == -1 ? color.green : thresholdState == 1 ? color.red : color.gray

plot(OverB, title="动态超买线", color=upperColor, linewidth=3)
plot(OverS, title="动态超卖线", color=lowerColor, linewidth=3)
hline(50, "中轨", color=color.new(#787B86, 50))
// 计算K线变化趋势
kChange = k - k[1]  // 当前K线相对前一根的变化
kChange2 = k[1] - k[2]  // 前一根K线的变化
kMomentum = kChange + kChange2  // 动量指标

// 提前信号检测
// 卖出提前信号：接近超买线且向下动量强劲
nearOverBought = k > (OverB - earlySignalDistance) and k < OverB
strongDownMomentum = kMomentum < -earlySignalThreshold
earlySellSignal = nearOverBought and strongDownMomentum

// 买入提前信号：接近超卖线且向上动量强劲
nearOverSold = k < (OverS + earlySignalDistance) and k > OverS
strongUpMomentum = kMomentum > earlySignalThreshold
earlyBuySignal = nearOverSold and strongUpMomentum

// 传统穿越信号（作为备用）
// 蓝色线（%K）上穿下虚线（超卖线）- 买入信号
normalBuySignal = ta.crossover(k, OverS)
// 蓝色线（%K）下穿上虚线（超买线）- 卖出信号
normalSellSignal = ta.crossunder(k, OverB)

// 综合信号
buySignal = earlyBuySignal or normalBuySignal
sellSignal = earlySellSignal or normalSellSignal

// 在图表上标记信号点
if earlyBuySignal
    label.new(bar_index, k, "早买", style=label.style_label_up, color=color.lime, textcolor=color.white, size=size.small)
else if normalBuySignal
    label.new(bar_index, k, "BUY", style=label.style_label_up, color=color.green, textcolor=color.white, size=size.small)

if earlySellSignal
    label.new(bar_index, k, "早卖", style=label.style_label_down, color=color.orange, textcolor=color.white, size=size.small)
else if normalSellSignal
    label.new(bar_index, k, "SELL", style=label.style_label_down, color=color.red, textcolor=color.white, size=size.small)

// === 策略部分 ===
// 蓝色线上穿下虚线时买入，下穿上虚线时卖出
if buySignal
    log.info("buy signal at {0}", close)
    strategy.entry("Buy", strategy.long, comment="K线上穿超卖线")

if sellSignal
    log.info("sell signal at {0}", close)
    strategy.close("Buy", comment="K线下穿超买线")
// 结束